import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';
import '../constants/app_constants.dart';

class WebSocketService {
  static final WebSocketService _instance = WebSocketService._internal();
  factory WebSocketService() => _instance;
  WebSocketService._internal();

  WebSocketChannel? _channel;
  StreamController<Map<String, dynamic>>? _messageController;
  StreamController<List<Map<String, dynamic>>>? _bidsController;
  StreamController<bool>? _connectionStatusController;
  String? _currentAuctionId;
  bool _isConnected = false;
  List<Map<String, dynamic>> _liveBids = [];
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  static const int maxReconnectAttempts = 5;

  // Stream for listening to WebSocket messages
  Stream<Map<String, dynamic>> get messageStream =>
      _messageController?.stream ?? const Stream.empty();

  // Stream for live bidding history
  Stream<List<Map<String, dynamic>>> get liveBidsStream =>
      _bidsController?.stream ?? const Stream.empty();

  // Stream for connection status
  Stream<bool> get connectionStatusStream =>
      _connectionStatusController?.stream ?? const Stream.empty();

  bool get isConnected => _isConnected;
  String? get currentAuctionId => _currentAuctionId;

  /// Connect to auction WebSocket
  Future<void> connectToAuction(String auctionId) async {
    try {
      // Disconnect from previous auction if connected
      if (_isConnected && _currentAuctionId != auctionId) {
        await disconnect();
      }

      // Don't reconnect if already connected to the same auction
      if (_isConnected && _currentAuctionId == auctionId) {
        print('🔌 Already connected to auction $auctionId');
        return;
      }

      _currentAuctionId = auctionId;
      _messageController = StreamController<Map<String, dynamic>>.broadcast();
      _bidsController = StreamController<List<Map<String, dynamic>>>.broadcast();
      _connectionStatusController = StreamController<bool>.broadcast();
      _liveBids.clear();
      _reconnectAttempts = 0;

      // Create WebSocket URL
      final wsUrl = '${AppConstants.wsUrl}/auction/$auctionId/';
      print('🔌 Connecting to WebSocket: $wsUrl');
      print('🔍 WebSocket URL details: ${AppConstants.wsUrl}');

      // Connect to WebSocket with authentication
      print('🔌 Attempting WebSocket connection...');
      _channel = IOWebSocketChannel.connect(
        Uri.parse(wsUrl),
        headers: {
          'Origin': 'http://localhost:8000',
          // Note: WebSocket authentication will be handled via API calls
          // WebSockets don't support Authorization headers in browsers
        },
      );

      print('✅ WebSocket channel created, waiting for connection...');

      // Listen to messages
      _channel!.stream.listen(
        (message) {
          try {
            final data = jsonDecode(message) as Map<String, dynamic>;
            print('📨 WebSocket message received: ${data['type']}');
            print('📋 Message data: $data');

            // Handle bid updates for live bidding history
            if (data['type'] == 'bid_update' && data['data'] != null) {
              print('💰 Processing bid update: ${data['data']}');
              _handleLiveBidUpdate(data['data']);
            }

            _messageController?.add(data);
          } catch (e) {
            print('❌ Error parsing WebSocket message: $e');
            print('📋 Raw message: $message');
          }
        },
        onError: (error) {
          print('❌ WebSocket error: $error');
          _isConnected = false;
          _connectionStatusController?.add(false);
          _handleConnectionError();
        },
        onDone: () {
          print('🔌 WebSocket connection closed');
          _isConnected = false;
          _connectionStatusController?.add(false);
          _handleConnectionClosed();
        },
      );

      _isConnected = true;
      _connectionStatusController?.add(true);
      print('✅ Connected to auction $auctionId WebSocket');

      // Start heartbeat to monitor connection
      _startHeartbeat();

      // Send join auction message
      await _sendMessage({
        'type': 'join_auction',
        'auction_id': auctionId,
      });

    } catch (e) {
      print('❌ Failed to connect to WebSocket: $e');
      _handleConnectionError();
    }
  }

  /// Disconnect from WebSocket
  Future<void> disconnect() async {
    try {
      print('🔌 Disconnecting from WebSocket...');
      
      _isConnected = false;
      _currentAuctionId = null;

      // Stop timers
      _heartbeatTimer?.cancel();
      _heartbeatTimer = null;
      _reconnectTimer?.cancel();
      _reconnectTimer = null;

      await _channel?.sink.close();
      _channel = null;

      await _messageController?.close();
      _messageController = null;

      await _bidsController?.close();
      _bidsController = null;

      _liveBids.clear();

      // Send disconnection status before closing controller
      if (_connectionStatusController != null && !_connectionStatusController!.isClosed) {
        _connectionStatusController!.add(false);
      }

      await _connectionStatusController?.close();
      _connectionStatusController = null;
      
      print('✅ WebSocket disconnected');
    } catch (e) {
      print('❌ Error disconnecting WebSocket: $e');
    }
  }

  /// Send message through WebSocket
  Future<void> _sendMessage(Map<String, dynamic> message) async {
    if (!_isConnected || _channel == null) {
      print('❌ Cannot send message: WebSocket not connected');
      return;
    }

    try {
      final jsonMessage = jsonEncode(message);
      _channel!.sink.add(jsonMessage);
      print('📤 Sent WebSocket message: ${message['type']}');
    } catch (e) {
      print('❌ Error sending WebSocket message: $e');
    }
  }

  /// Place bid through WebSocket
  Future<void> placeBid(double amount) async {
    await _sendMessage({
      'type': 'place_bid',
      'amount': amount,
    });
  }

  /// Handle connection error
  void _handleConnectionError() {
    _isConnected = false;

    // Safely update connection status
    if (_connectionStatusController != null && !_connectionStatusController!.isClosed) {
      _connectionStatusController!.add(false);
    }

    // Safely add error to message controller
    if (_messageController != null && !_messageController!.isClosed) {
      _messageController!.addError('WebSocket connection error');
    }

    // Attempt reconnection only if we have an auction ID
    if (_currentAuctionId != null) {
      _attemptReconnection();
    } else {
      print('❌ Cannot attempt reconnection: no auction ID');
    }
  }

  /// Handle connection closed
  void _handleConnectionClosed() {
    _isConnected = false;

    // Safely update connection status
    if (_connectionStatusController != null && !_connectionStatusController!.isClosed) {
      _connectionStatusController!.add(false);
    }

    // Safely add connection closed message
    if (_messageController != null && !_messageController!.isClosed) {
      _messageController!.add({
        'type': 'connection_closed',
        'message': 'WebSocket connection closed'
      });
    }

    // Attempt reconnection only if we have an auction ID
    if (_currentAuctionId != null) {
      _attemptReconnection();
    } else {
      print('❌ Cannot attempt reconnection: no auction ID');
    }
  }

  /// Reconnect to current auction
  Future<void> reconnect() async {
    final auctionId = _currentAuctionId;
    if (auctionId != null) {
      print('🔄 Reconnecting to auction $auctionId...');
      await disconnect();
      await Future.delayed(const Duration(seconds: 2));
      await connectToAuction(auctionId);
    } else {
      print('❌ Cannot reconnect: no auction ID available');
    }
  }

  /// Handle live bid updates for real-time bidding history
  void _handleLiveBidUpdate(Map<String, dynamic> bidData) {
    // Add new bid to the beginning of the list (most recent first)
    _liveBids.insert(0, {
      'id': bidData['bid_id'],
      'amount': bidData['amount'],
      'bidder': bidData['bidder'],
      'timestamp': bidData['timestamp'],
      'bid_type': bidData['bid_type'] ?? 'manual',
      'is_winning': bidData['is_winning'] ?? false,
    });

    // Keep only last 100 bids for performance
    if (_liveBids.length > 100) {
      _liveBids = _liveBids.take(100).toList();
    }

    // Broadcast updated bids list
    _bidsController?.add(List.from(_liveBids));
  }

  /// Get current live bids
  List<Map<String, dynamic>> get currentBids => List.from(_liveBids);

  /// Clear live bids
  void clearBids() {
    _liveBids.clear();
    _bidsController?.add([]);
  }

  /// Start heartbeat to monitor connection
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isConnected && _channel != null) {
        _sendMessage({'type': 'ping'});
      } else {
        timer.cancel();
      }
    });
  }

  /// Handle automatic reconnection
  void _attemptReconnection() {
    if (_reconnectAttempts >= maxReconnectAttempts || _currentAuctionId == null) {
      print('❌ Max reconnection attempts reached or no auction ID');
      return;
    }

    final auctionId = _currentAuctionId;
    if (auctionId == null) {
      print('❌ Cannot reconnect: auction ID is null');
      return;
    }

    _reconnectAttempts++;
    final delay = Duration(seconds: _reconnectAttempts * 2); // Exponential backoff

    print('🔄 Attempting reconnection $_reconnectAttempts/$maxReconnectAttempts in ${delay.inSeconds}s');

    _reconnectTimer = Timer(delay, () async {
      try {
        await connectToAuction(auctionId);
      } catch (e) {
        print('❌ Reconnection attempt $_reconnectAttempts failed: $e');
        _attemptReconnection();
      }
    });
  }

  /// Handle app going to background
  void onAppPaused() {
    print('📱 App paused - maintaining WebSocket connection');
    // Keep connection alive but reduce heartbeat frequency
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      if (_isConnected && _channel != null) {
        _sendMessage({'type': 'ping'});
      } else {
        timer.cancel();
      }
    });
  }

  /// Handle app coming to foreground
  void onAppResumed() {
    print('📱 App resumed - checking WebSocket connection');
    final auctionId = _currentAuctionId;
    if (auctionId != null && !_isConnected) {
      // Reconnect if disconnected
      connectToAuction(auctionId);
    } else if (_isConnected) {
      // Resume normal heartbeat
      _startHeartbeat();
    }
  }

  /// Dispose resources
  void dispose() {
    disconnect();
  }
}

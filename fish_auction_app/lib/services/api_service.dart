
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../utils/api_response.dart';


class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  late Dio _dio;
  String? _authToken;
  String? _refreshToken;

  void initialize() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: Duration(milliseconds: AppConstants.connectionTimeout),
      receiveTimeout: Duration(milliseconds: AppConstants.requestTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'ngrok-skip-browser-warning': 'true', // Skip ngrok browser warning
      },
    ));

    // Add interceptors
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token to requests
        if (_authToken != null) {
          options.headers['Authorization'] = 'Bearer $_authToken';
        }
        
        print('🚀 ${options.method} ${options.path}');
        if (options.data != null) {
          print('📤 Request Data: ${options.data}');
        }
        
        handler.next(options);
      },
      onResponse: (response, handler) {
        print('✅ ${response.statusCode} ${response.requestOptions.path}');
        handler.next(response);
      },
      onError: (error, handler) async {
        // Don't log 404 errors for auto-bid checks as they're expected
        if (error.response?.statusCode == 404 &&
            error.requestOptions.path.contains('/auto-bid/') &&
            error.requestOptions.method == 'GET') {
          // This is expected when no auto-bid exists
        } else {
          print('❌ ${error.response?.statusCode} ${error.requestOptions.path}');
          print('Error: ${error.message}');
        }

        // Handle token refresh on 401, but not for the refresh endpoint itself
        if (error.response?.statusCode == 401 &&
            _refreshToken != null &&
            !error.requestOptions.path.contains('/token/refresh/')) {
          try {
            await _refreshAuthToken();
            // Retry the original request
            final options = error.requestOptions;
            options.headers['Authorization'] = 'Bearer $_authToken';
            final response = await _dio.fetch(options);
            handler.resolve(response);
            return;
          } catch (e) {
            print('Token refresh failed: $e');
            // Refresh failed, clear tokens and redirect to login
            await clearAuthTokens();
          }
        }

        // If this is a 401 on the refresh endpoint, clear tokens
        if (error.response?.statusCode == 401 &&
            error.requestOptions.path.contains('/token/refresh/')) {
          print('Refresh token expired, clearing tokens');
          await clearAuthTokens();
        }

        handler.next(error);
      },
    ));
  }

  // Auth Token Management
  Future<void> setAuthTokens(String accessToken, String refreshToken) async {
    _authToken = accessToken;
    _refreshToken = refreshToken;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.tokenKey, accessToken);
    await prefs.setString(AppConstants.refreshTokenKey, refreshToken);
  }

  Future<void> loadAuthTokens() async {
    final prefs = await SharedPreferences.getInstance();
    _authToken = prefs.getString(AppConstants.tokenKey);
    _refreshToken = prefs.getString(AppConstants.refreshTokenKey);
  }

  Future<void> clearAuthTokens() async {
    _authToken = null;
    _refreshToken = null;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.tokenKey);
    await prefs.remove(AppConstants.refreshTokenKey);
    await prefs.remove(AppConstants.userKey);
  }

  Future<void> _refreshAuthToken() async {
    if (_refreshToken == null) throw Exception('No refresh token available');

    try {
      // Create a new Dio instance without interceptors to avoid infinite loop
      final refreshDio = Dio();
      refreshDio.options.baseUrl = _dio.options.baseUrl;
      refreshDio.options.headers = {
        'Content-Type': 'application/json',
      };

      final response = await refreshDio.post(
        '${AppConstants.authEndpoint}/token/refresh/',
        data: {'refresh': _refreshToken},
      );

      if (response.statusCode == 200) {
        final data = response.data;
        _authToken = data['access'];

        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(AppConstants.tokenKey, _authToken!);

        // Update the main Dio instance with new token
        _dio.options.headers['Authorization'] = 'Bearer $_authToken';
      } else {
        throw Exception('Failed to refresh token: ${response.statusCode}');
      }
    } catch (e) {
      print('Token refresh error: $e');
      throw Exception('Token refresh failed: $e');
    }
  }

  bool get isAuthenticated => _authToken != null;

  // Generic API Methods
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
      );
      
      return ApiResponse<T>.success(
        data: fromJson != null ? fromJson(response.data) : response.data,
        statusCode: response.statusCode ?? 200,
      );
    } on DioException catch (e) {
      return _handleDioError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }

  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );
      
      return ApiResponse<T>.success(
        data: fromJson != null ? fromJson(response.data) : response.data,
        statusCode: response.statusCode ?? 200,
      );
    } on DioException catch (e) {
      return _handleDioError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }

  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );
      
      return ApiResponse<T>.success(
        data: fromJson != null ? fromJson(response.data) : response.data,
        statusCode: response.statusCode ?? 200,
      );
    } on DioException catch (e) {
      return _handleDioError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }

  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        endpoint,
        queryParameters: queryParameters,
      );

      return ApiResponse<T>.success(
        data: fromJson != null ? fromJson(response.data) : response.data,
        statusCode: response.statusCode ?? 200,
      );
    } on DioException catch (e) {
      return _handleDioError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }

  Future<ApiResponse<T>> patch<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.patch(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );

      return ApiResponse<T>.success(
        data: fromJson != null ? fromJson(response.data) : response.data,
        statusCode: response.statusCode ?? 200,
      );
    } on DioException catch (e) {
      return _handleDioError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }

  // File Upload
  Future<ApiResponse<T>> uploadFile<T>(
    String endpoint,
    File file, {
    String fieldName = 'file',
    Map<String, dynamic>? additionalData,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final formData = FormData();
      
      // Add file
      formData.files.add(MapEntry(
        fieldName,
        await MultipartFile.fromFile(file.path),
      ));
      
      // Add additional data
      if (additionalData != null) {
        additionalData.forEach((key, value) {
          formData.fields.add(MapEntry(key, value.toString()));
        });
      }
      
      final response = await _dio.post(
        endpoint,
        data: formData,
      );
      
      return ApiResponse<T>.success(
        data: fromJson != null ? fromJson(response.data) : response.data,
        statusCode: response.statusCode ?? 200,
      );
    } on DioException catch (e) {
      return _handleDioError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error(
        message: e.toString(),
        statusCode: 500,
      );
    }
  }

  ApiResponse<T> _handleDioError<T>(DioException e) {
    String message = AppConstants.unknownError;
    int statusCode = 500;
    
    if (e.response != null) {
      statusCode = e.response!.statusCode ?? 500;
      
      // Try to extract error message from response
      try { 
        final data = e.response!.data;
        if (data is Map<String, dynamic>) {
          if (data.containsKey('detail')) {
            message = data['detail'].toString();
          } else if (data.containsKey('message')) {
            message = data['message'].toString();
          } else if (data.containsKey('error')) {
            message = data['error'].toString();
          } else {
            // Handle field-specific errors
            final errors = <String>[];
            data.forEach((key, value) {
              if (value is List) {
                errors.addAll(value.map((e) => e.toString()));
              } else {
                errors.add(value.toString());
              }
            });
            if (errors.isNotEmpty) {
              message = errors.join(', ');
            }
          }
        } else {
          message = data.toString();
        }
      } catch (_) {
        message = 'Server error (${statusCode})';
      }
    } else {
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          message = AppConstants.timeoutError;
          break;
        case DioExceptionType.connectionError:
          message = AppConstants.networkError;
          break;
        default:
          message = AppConstants.unknownError;
      }
    }
    
    return ApiResponse<T>.error(
      message: message,
      statusCode: statusCode,
    );
  }
}

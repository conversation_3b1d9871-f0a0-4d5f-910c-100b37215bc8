import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:async';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';
import '../../models/bid_model.dart';
import '../../providers/auction_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/websocket_service.dart';
import '../../utils/app_localizations.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../widgets/auction/live_bidding_history.dart';
import '../../widgets/auction/auto_bid_manager.dart';
import '../../widgets/auction/auction_status_manager.dart';

class AuctionDetailScreen extends StatefulWidget {
  final Auction auction;

  const AuctionDetailScreen({
    super.key,
    required this.auction,
  });

  @override
  State<AuctionDetailScreen> createState() => _AuctionDetailScreenState();
}

class _AuctionDetailScreenState extends State<AuctionDetailScreen>
    with TickerProviderStateMixin {
  TabController? _tabController;
  final PageController _imageController = PageController();
  int _currentImageIndex = 0;

  // WebSocket related
  final WebSocketService _webSocketService = WebSocketService();
  StreamSubscription<Map<String, dynamic>>? _webSocketSubscription;
  late Auction _currentAuction;

  // Timer related
  Timer? _countdownTimer;
  Timer? _fallbackTimer;
  Duration _timeRemaining = Duration.zero;

  @override
  void initState() {
    super.initState();
    _currentAuction = widget.auction;
    _timeRemaining = _currentAuction.timeRemaining;
    _loadAuctionDetails();
    _initializeWebSocket();
    _startCountdownTimer();
    _startFallbackPolling();
  }

  void _initializeTabController() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isOwner = authProvider.user?.id == widget.auction.seller.id;
    final tabCount = isOwner ? 3 : 4; // Sellers get 3 tabs, buyers get 4

    _tabController?.dispose();
    _tabController = TabController(length: tabCount, vsync: this);
  }

  @override
  void dispose() {
    _tabController?.dispose();
    _imageController.dispose();
    _webSocketSubscription?.cancel();
    _webSocketService.disconnect();
    _countdownTimer?.cancel();
    _stopFallbackPolling();
    super.dispose();
  }

  Future<void> _loadAuctionDetails() async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    await auctionProvider.loadAuctionDetails(widget.auction.id);
  }

  /// Initialize WebSocket connection for real-time updates
  Future<void> _initializeWebSocket() async {
    try {
      // Connect to auction WebSocket
      await _webSocketService.connectToAuction(_currentAuction.id.toString());

      // Listen to WebSocket messages
      _webSocketSubscription = _webSocketService.messageStream.listen(
        _handleWebSocketMessage,
        onError: (error) {
          print('❌ WebSocket stream error: $error');
        },
      );

      print('✅ WebSocket initialized for auction ${_currentAuction.id}');
    } catch (e) {
      print('❌ Failed to initialize WebSocket: $e');
    }
  }

  /// Handle incoming WebSocket messages
  void _handleWebSocketMessage(Map<String, dynamic> message) {
    final messageType = message['type'];
    final data = message['data'];

    print('📨 Received WebSocket message: $messageType');

    switch (messageType) {
      case 'bid_update':
        _handleBidUpdate(data);
        break;
      case 'auction_status':
        _handleAuctionStatusUpdate(data);
        break;
      case 'user_joined':
        _handleUserJoined(data);
        break;
      case 'auction_ended':
        _handleAuctionEnded(data);
        break;
      case 'connection_closed':
        _handleConnectionClosed();
        break;
      default:
        print('🔍 Unknown WebSocket message type: $messageType');
    }
  }

  /// Handle bid update from WebSocket
  void _handleBidUpdate(Map<String, dynamic>? data) {
    if (data == null) return;

    setState(() {
      // Update current auction with new bid data
      _currentAuction = _currentAuction.copyWith(
        currentPrice: double.tryParse(data['current_price']?.toString() ?? '0') ?? _currentAuction.currentPrice,
        totalBids: data['total_bids'] ?? _currentAuction.totalBids,
      );
    });

    // Update timer in case auction status changed
    _updateTimer();

    // Don't reload bids from API - WebSocket service handles bid updates
    // The LiveBiddingHistory widget will automatically update via WebSocket stream

    // Show notification for bid updates
    final bidType = data['bid_type']?.toString() ?? 'manual';
    final isAutoBid = bidType == 'auto';
    final amount = data['amount']?.toString() ?? '0';
    final bidder = data['bidder']?.toString() ?? 'Unknown';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '${isAutoBid ? '🤖 Auto-bid' : '💰 New bid'}: \$${amount} by ${bidder}',
          style: const TextStyle(color: Colors.white),
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: isAutoBid ? Colors.orange : AppColors.success,
      ),
    );
  }

  /// Handle auction status update
  void _handleAuctionStatusUpdate(Map<String, dynamic>? data) {
    if (data == null) return;

    setState(() {
      _currentAuction = _currentAuction.copyWith(
        currentPrice: double.tryParse(data['current_price']?.toString() ?? '0') ?? _currentAuction.currentPrice,
        totalBids: data['total_bids'] ?? _currentAuction.totalBids,
        status: data['status'] ?? _currentAuction.status,
      );
    });
  }

  /// Handle user joined notification
  void _handleUserJoined(Map<String, dynamic>? data) {
    if (data == null) return;

    // Optionally show user joined notification
    print('👤 User joined: ${data['username']} (${data['user_type']})');
  }

  /// Handle auction ended notification
  void _handleAuctionEnded(Map<String, dynamic>? data) {
    if (data == null) return;

    setState(() {
      _currentAuction = _currentAuction.copyWith(status: 'ended');
    });

    // Show auction ended dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Auction Ended'),
        content: const Text('This auction has ended. Thank you for participating!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Handle WebSocket connection closed
  void _handleConnectionClosed() {
    print('🔌 WebSocket connection closed, attempting to reconnect...');

    // Attempt to reconnect after a delay
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _webSocketService.reconnect();
      }
    });
  }

  /// Start countdown timer for real-time updates
  void _startCountdownTimer() {
    _countdownTimer?.cancel();

    if (_currentAuction.isLive && _currentAuction.hasTimeRemaining) {
      _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (mounted) {
          setState(() {
            _timeRemaining = _currentAuction.timeRemaining;

            // Stop timer if auction ended
            if (_timeRemaining.inSeconds <= 0) {
              timer.cancel();
              _currentAuction = _currentAuction.copyWith(status: 'ended');
            }
          });
        } else {
          timer.cancel();
        }
      });
    }
  }

  /// Stop countdown timer
  void _stopCountdownTimer() {
    _countdownTimer?.cancel();
    _countdownTimer = null;
  }

  /// Update timer when auction data changes
  void _updateTimer() {
    if (_currentAuction.isLive && _currentAuction.hasTimeRemaining) {
      _timeRemaining = _currentAuction.timeRemaining;
      if (_countdownTimer == null || !_countdownTimer!.isActive) {
        _startCountdownTimer();
      }
    } else {
      _stopCountdownTimer();
    }
  }

  /// Start fallback polling for real-time updates (backup for WebSocket)
  void _startFallbackPolling() {
    _fallbackTimer?.cancel();

    if (_currentAuction.isLive) {
      _fallbackTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
        if (mounted && _currentAuction.isLive) {
          _refreshAuctionData();
        } else {
          timer.cancel();
        }
      });
      print('🔄 Started fallback polling for auction ${_currentAuction.id}');
    }
  }

  /// Stop fallback polling
  void _stopFallbackPolling() {
    _fallbackTimer?.cancel();
    _fallbackTimer = null;
    print('🛑 Stopped fallback polling');
  }

  /// Refresh auction data from API (fallback for WebSocket)
  Future<void> _refreshAuctionData() async {
    try {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      await auctionProvider.loadAuctionDetails(_currentAuction.id);

      final updatedAuction = auctionProvider.selectedAuction;
      if (mounted && updatedAuction != null) {
        // Only update if the data has actually changed
        if (updatedAuction.currentPrice != _currentAuction.currentPrice ||
            updatedAuction.totalBids != _currentAuction.totalBids) {
          setState(() {
            _currentAuction = updatedAuction;
          });
          print('🔄 Auction data refreshed: \$${updatedAuction.currentPrice}, ${updatedAuction.totalBids} bids');
        }
      }
    } catch (e) {
      print('❌ Error refreshing auction data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      body: Consumer<AuctionProvider>(
        builder: (context, auctionProvider, child) {
          // Always use the current auction state (updated by WebSocket)
          // Don't override with provider data as it might be stale
          final auction = _currentAuction;
          
          return LoadingOverlay(
            isLoading: auctionProvider.isLoading,
            child: CustomScrollView(
              slivers: [
                // App Bar with Images
                _buildSliverAppBar(auction, localizations),
                
                // Content
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      // Auction Info
                      _buildAuctionInfo(auction, localizations),

                      // Auction Status Manager (for sellers only)
                      _buildAuctionStatusManager(auction),

                      // Action Buttons
                      _buildActionButtons(auction, localizations),

                      // Tabs
                      _buildTabSection(auction, localizations),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSliverAppBar(Auction auction, AppLocalizations localizations) {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: _buildImageCarousel(auction),
      ),
      actions: [
        IconButton(
          onPressed: () => _toggleWatchlist(auction),
          icon: Icon(
            auction.isWatched ? Icons.bookmark : Icons.bookmark_outline,
            color: auction.isWatched ? AppColors.accent : AppColors.textLight,
          ),
        ),
        IconButton(
          onPressed: () => _shareAuction(auction),
          icon: const Icon(Icons.share, color: AppColors.textLight),
        ),
      ],
    );
  }

  Widget _buildImageCarousel(Auction auction) {
    // Use images if available, otherwise use a placeholder
    List<String> images = [];
    if (auction.images.isNotEmpty) {
      images = auction.images;
    } else {
      // Use placeholder image if no images available
      images = ['https://via.placeholder.com/400x300?text=Fish+Image'];
    }

    return Stack(
      children: [
        PageView.builder(
          controller: _imageController,
          onPageChanged: (index) {
            setState(() {
              _currentImageIndex = index;
            });
          },
          itemCount: images.length,
          itemBuilder: (context, index) {
            final imageUrl = images[index];
            return CachedNetworkImage(
              imageUrl: imageUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: AppColors.background,
                child: const Center(child: CircularProgressIndicator()),
              ),
              errorWidget: (context, url, error) => Container(
                color: AppColors.background,
                child: const Center(
                  child: Icon(Icons.image_outlined, size: 64, color: AppColors.textHint),
                ),
              ),
            );
          },
        ),
        
        // Image Indicators
        if (images.length > 1)
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: images.asMap().entries.map((entry) {
                return Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentImageIndex == entry.key
                        ? AppColors.textLight
                        : AppColors.textLight.withOpacity(0.4),
                  ),
                );
              }).toList(),
            ),
          ),
        
        // Status Badge
        Positioned(
          top: 60,
          left: 16,
          child: _buildStatusBadge(auction),
        ),
      ],
    );
  }

  Widget _buildStatusBadge(Auction auction) {
    Color statusColor;
    String statusText;
    
    switch (auction.status) {
      case 'live':
        statusColor = AppColors.liveAuction;
        statusText = 'LIVE';
        break;
      case 'scheduled':
        statusColor = AppColors.scheduledAuction;
        statusText = 'SCHEDULED';
        break;
      case 'ended':
        statusColor = AppColors.endedAuction;
        statusText = 'ENDED';
        break;
      default:
        statusColor = AppColors.textSecondary;
        statusText = auction.status.toUpperCase();
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: statusColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        statusText,
        style: const TextStyle(
          color: AppColors.textLight,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildAuctionInfo(Auction auction, AppLocalizations localizations) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            auction.title,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          
          const SizedBox(height: AppConstants.smallPadding),
          
          // Fish Details
          Row(
            children: [
              Icon(Icons.category_outlined, size: 16, color: AppColors.textSecondary),
              const SizedBox(width: 4),
              Text(
                auction.fishType,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
              ),
              const SizedBox(width: 16),
              Icon(Icons.scale_outlined, size: 16, color: AppColors.textSecondary),
              const SizedBox(width: 4),
              Text(
                auction.formattedQuantity,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
              ),
              const SizedBox(width: 16),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Grade ${auction.qualityGrade}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Price and Bids
          Row(
            children: [
              Expanded(
                child: _buildInfoCard(
                  title: 'Current Price',
                  value: '\$${auction.currentPrice.toStringAsFixed(2)}',
                  color: AppColors.primary,
                  icon: Icons.attach_money,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _buildInfoCard(
                  title: 'Total Bids',
                  value: auction.totalBids.toString(),
                  color: AppColors.secondary,
                  icon: Icons.gavel,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Time Remaining
          if (auction.isLive && auction.hasTimeRemaining)
            _buildTimeRemainingCard(auction),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required String value,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTimeRemainingCard(Auction auction) {
    // Use real-time timer value instead of static auction time
    final timeRemaining = _timeRemaining.inSeconds > 0 ? _timeRemaining : auction.timeRemaining;
    final isEndingSoon = timeRemaining.inHours < 1;
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: isEndingSoon 
            ? AppColors.error.withOpacity(0.1)
            : AppColors.warning.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: isEndingSoon ? AppColors.error : AppColors.warning,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.schedule,
            color: isEndingSoon ? AppColors.error : AppColors.warning,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Text(
            'Ends in ${_formatTimeRemaining(timeRemaining)}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: isEndingSoon ? AppColors.error : AppColors.warning,
                  fontWeight: FontWeight.w600,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildAuctionStatusManager(Auction auction) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isOwner = authProvider.user?.id == auction.seller.id;

    // Only show status manager for sellers
    if (!isOwner) {
      return const SizedBox.shrink();
    }

    return AuctionStatusManager(
      auction: auction,
      onStatusChanged: () {
        // Refresh auction details when status changes
        _loadAuctionDetails();
        setState(() {
          // Update current auction state
          _currentAuction = auction;
        });
      },
    );
  }

  Widget _buildActionButtons(Auction auction, AppLocalizations localizations) {
    final authProvider = Provider.of<AuthProvider>(context);

    // Check if current user is the seller
    final isOwner = authProvider.user?.id == auction.seller.id;

    // Show bidding buttons for live auctions OR draft auctions (which should be live)
    // Also check if user can bid (is buyer) and is not the seller
    final canShowBidding = (auction.isLive || auction.isDraft) && authProvider.canBid && !isOwner;

    if (!canShowBidding) {
      // Show appropriate message based on why bidding is not available
      String message;
      Color color;
      IconData icon;

      if (isOwner) {
        message = 'Your Auction';
        color = AppColors.info;
        icon = Icons.store;
      } else if (auction.isEnded) {
        message = 'Auction Ended';
        color = AppColors.textSecondary;
        icon = Icons.gavel_outlined;
      } else if (!auction.isLive && !auction.isDraft) {
        message = 'Auction ${auction.status}';
        color = AppColors.textSecondary;
        icon = Icons.info_outline;
      } else {
        message = 'Bidding not available';
        color = AppColors.warning;
        icon = Icons.info_outline;
      }

      return Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color),
              const SizedBox(width: AppConstants.smallPadding),
              Text(
                message,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Expanded(
            child: PrimaryButton(
              text: localizations.placeBid,
              onPressed: () => _showBidDialog(auction, localizations),
              icon: Icons.gavel,
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          OutlinedCustomButton(
            text: localizations.autoBid,
            onPressed: () => _showAutoBidDialog(auction, localizations),
            icon: Icons.auto_mode,
            width: 120,
          ),
        ],
      ),
    );
  }

  Widget _buildTabSection(Auction auction, AppLocalizations localizations) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isOwner = authProvider.user?.id == auction.seller.id;

    // Initialize tab controller if needed
    if (_tabController == null) {
      _initializeTabController();
    }

    // Different tabs for sellers vs buyers
    final tabs = isOwner
        ? [
            Tab(text: localizations.auctionDetails),
            Tab(text: 'Live Bidding'),
            Tab(text: 'Seller Info'),
          ]
        : [
            Tab(text: localizations.auctionDetails),
            Tab(text: 'Live Bidding'),
            Tab(text: 'Auto Bid'),
            Tab(text: 'Seller Info'),
          ];

    final tabViews = isOwner
        ? [
            _buildDetailsTab(auction, localizations),
            _buildLiveBiddingTab(auction),
            _buildSellerInfoTab(auction, localizations),
          ]
        : [
            _buildDetailsTab(auction, localizations),
            _buildLiveBiddingTab(auction),
            _buildAutoBidTab(auction),
            _buildSellerInfoTab(auction, localizations),
          ];

    return Column(
      children: [
        TabBar(
          controller: _tabController!,
          tabs: tabs,
        ),
        SizedBox(
          height: 400,
          child: TabBarView(
            controller: _tabController!,
            children: tabViews,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailsTab(Auction auction, AppLocalizations localizations) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (auction.description.isNotEmpty) ...[
            Text(
              localizations.description,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              auction.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
          ],
          
          // Auction Details
          _buildDetailRow('Starting Price', '\$${auction.startingPrice.toStringAsFixed(2)}'),
          _buildDetailRow('Reserve Price', '\$${auction.reservePrice?.toStringAsFixed(2) ?? 'N/A'}'),
          _buildDetailRow('Bid Increment', '\$${auction.bidIncrement.toStringAsFixed(2)}'),
          _buildDetailRow('Location', auction.location ?? 'Not specified'),
          _buildDetailRow('Created', auction.formattedCreatedAt),
          
          if (auction.specialNotes?.isNotEmpty == true) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              localizations.specialNotes,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: AppColors.warning.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(color: AppColors.warning.withOpacity(0.3)),
              ),
              child: Text(
                auction.specialNotes ?? '',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBiddingHistoryTab(AppLocalizations localizations) {
    return Consumer<AuctionProvider>(
      builder: (context, auctionProvider, child) {
        final bids = auctionProvider.selectedAuctionBids;
        
        if (bids.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.gavel_outlined,
                  size: 48,
                  color: AppColors.textHint,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  localizations.noBids,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppColors.textHint,
                      ),
                ),
              ],
            ),
          );
        }
        
        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          itemCount: bids.length,
          itemBuilder: (context, index) {
            final bid = bids[index];
            return ListTile(
              leading: CircleAvatar(
                backgroundColor: AppColors.primary.withOpacity(0.1),
                child: Text(
                  bid.bidderName[0].toUpperCase(),
                  style: const TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              title: Text(
                '\$${bid.amount.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              subtitle: Text(bid.bidderName),
              trailing: Text(
                bid.formattedTimestamp,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildLiveBiddingTab(Auction auction) {
    return LiveBiddingHistory(
      auctionId: auction.id,
      showAutoBids: true,
    );
  }

  Widget _buildAutoBidTab(Auction auction) {
    return AutoBidManager(
      auction: auction,
    );
  }

  Widget _buildSellerInfoTab(Auction auction, AppLocalizations localizations) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Seller Avatar and Info
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: AppColors.primary.withOpacity(0.1),
                child: Text(
                  auction.sellerName[0].toUpperCase(),
                  style: const TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 24,
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      auction.sellerName,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    Text(
                      'Verified Seller',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.success,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Contact Button
          PrimaryButton(
            text: 'Contact Seller',
            onPressed: () => _contactSeller(auction),
            icon: Icons.message,
          ),
        ],
      ),
    );
  }

  String _formatTimeRemaining(Duration duration) {
    if (duration.inSeconds <= 0) {
      return 'Auction Ended';
    } else if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h ${duration.inMinutes % 60}m';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m ${duration.inSeconds % 60}s';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  void _toggleWatchlist(Auction auction) {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    auctionProvider.toggleWatchlist(auction.id);
  }

  void _shareAuction(Auction auction) {
    final String shareText = '''
Check out this auction: ${auction.title}

${auction.description}

Current Price: \$${auction.currentPrice.toStringAsFixed(2)}
Fish Type: ${auction.fishType}
Quantity: ${auction.formattedQuantity}
Quality: ${auction.qualityGrade}

Ends: ${auction.endTime.toString()}

Download Fish Auction App to bid!
''';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Auction'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Copy Link'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Copy auction link to clipboard
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Link copied to clipboard')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share via...'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Use platform share functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Share functionality coming soon')),
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showBidDialog(Auction auction, AppLocalizations localizations) {
    final TextEditingController bidController = TextEditingController();
    final double minBid = auction.currentPrice + auction.bidIncrement;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.placeBid),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${localizations.currentPrice}: \$${auction.currentPrice.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Minimum Bid: \$${minBid.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: bidController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: localizations.bidAmount,
                prefixText: '\$',
                border: const OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          ElevatedButton(
            onPressed: () async {
              final bidAmount = double.tryParse(bidController.text);
              if (bidAmount != null && bidAmount >= minBid) {
                Navigator.of(context).pop();

                // Use API bidding for proper authentication
                final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
                final success = await auctionProvider.placeBid(auction.id, bidAmount);
                if (mounted) {
                  if (success) {
                    // Refresh auction data and bidding history
                    await auctionProvider.loadAuctionDetails(auction.id);

                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Bid placed successfully!')),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(auctionProvider.error ?? 'Failed to place bid')),
                    );
                  }
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Please enter a valid bid amount (minimum \$${minBid.toStringAsFixed(2)})')),
                );
              }
            },
            child: Text(localizations.placeBid),
          ),
        ],
      ),
    );
  }

  void _showAutoBidDialog(Auction auction, AppLocalizations localizations) {
    final TextEditingController maxAmountController = TextEditingController();
    final double minAmount = auction.currentPrice + auction.bidIncrement;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.autoBid),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${localizations.currentPrice}: \$${auction.currentPrice.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Minimum Amount: \$${minAmount.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: maxAmountController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Maximum Bid Amount',
                prefixText: '\$',
                border: OutlineInputBorder(),
                helperText: 'We will automatically bid up to this amount',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          ElevatedButton(
            onPressed: () async {
              final maxAmount = double.tryParse(maxAmountController.text);
              if (maxAmount != null && maxAmount >= minAmount) {
                Navigator.of(context).pop();
                final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
                final success = await auctionProvider.setAutoBid(auction.id, maxAmount);
                if (mounted) {
                  if (success) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Auto bid set successfully!')),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(auctionProvider.error ?? 'Failed to set auto bid')),
                    );
                  }
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Please enter a valid amount (minimum \$${minAmount.toStringAsFixed(2)})')),
                );
              }
            },
            child: Text(localizations.autoBid),
          ),
        ],
      ),
    );
  }

  void _contactSeller(Auction auction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Contact ${auction.sellerName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.phone),
              title: const Text('Call Seller'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Implement phone call functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Phone call feature coming soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.message),
              title: const Text('Send Message'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Implement messaging functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Messaging feature coming soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.email),
              title: const Text('Send Email'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Implement email functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Email feature coming soon')),
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

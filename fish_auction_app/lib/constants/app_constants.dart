class AppConstants {
  // API Configuration
  // Use ngrok URL for mobile testing or your actual server IP
  static const String baseUrl = 'https://3713-197-14-121-2.ngrok-free.app/api';
  static const String wsUrl = 'wss://3713-197-14-121-2.ngrok-free.app/ws';
  
  // API Endpoints
  static const String authEndpoint = '/auth';
  static const String auctionsEndpoint = '/auctions';
  static const String paymentsEndpoint = '/payments';
  static const String deliveryEndpoint = '/delivery';
  static const String notificationsEndpoint = '/notifications';
  static const String marketplaceEndpoint = '/marketplace';
  static const String supportEndpoint = '/support';
  
  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userKey = 'user_data';
  static const String languageKey = 'selected_language';
  static const String themeKey = 'theme_mode';
  
  // App Configuration
  static const String appName = 'Fish Auction';
  static const String appVersion = '1.0.0';
  static const int requestTimeout = 30000; // 30 seconds
  static const int connectionTimeout = 15000; // 15 seconds
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // File Upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png'];
  static const List<String> allowedDocumentTypes = ['pdf', 'jpg', 'jpeg', 'png'];
  
  // Auction Configuration
  static const int auctionRefreshInterval = 5; // seconds
  static const int bidTimeoutSeconds = 30;
  static const double minBidIncrement = 1.0;
  
  // UI Configuration
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 20.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Supported Languages
  static const List<String> supportedLanguages = ['en', 'ar'];
  static const String defaultLanguage = 'en';
  
  // Error Messages
  static const String networkError = 'Network connection error';
  static const String serverError = 'Server error occurred';
  static const String unknownError = 'An unknown error occurred';
  static const String timeoutError = 'Request timeout';
  static const String authError = 'Authentication failed';
  
  // Success Messages
  static const String loginSuccess = 'Login successful';
  static const String registrationSuccess = 'Registration successful';
  static const String bidPlacedSuccess = 'Bid placed successfully';
  static const String paymentSuccess = 'Payment completed successfully';
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 30;
  
  // Phone Number Validation
  static const String phoneRegex = r'^\+?[1-9]\d{1,14}$';
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  
  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';
  static const String displayDateFormat = 'MMM dd, yyyy';
  static const String displayTimeFormat = 'hh:mm a';
  static const String displayDateTimeFormat = 'MMM dd, yyyy hh:mm a';
  
  // Currency
  static const String defaultCurrency = 'USD';
  static const String currencySymbol = '\$';
  
  // Map Configuration
  static const double defaultLatitude = 25.2048; // UAE
  static const double defaultLongitude = 55.2708;
  static const double defaultZoom = 10.0;
  
  // Notification Types
  static const String auctionStarted = 'auction_started';
  static const String auctionEnding = 'auction_ending';
  static const String bidOutbid = 'bid_outbid';
  static const String auctionWon = 'auction_won';
  static const String paymentReminder = 'payment_reminder';
  static const String deliveryUpdate = 'delivery_update';
  
  // User Types
  static const String buyerType = 'buyer';
  static const String sellerType = 'seller';
  static const String brokerType = 'broker';
  static const String serviceProviderType = 'service_provider';
  static const String adminType = 'admin';
  
  // Auction Status
  static const String draftStatus = 'draft';
  static const String scheduledStatus = 'scheduled';
  static const String liveStatus = 'live';
  static const String endedStatus = 'ended';
  static const String cancelledStatus = 'cancelled';
  
  // Payment Status
  static const String pendingPayment = 'pending';
  static const String completedPayment = 'completed';
  static const String failedPayment = 'failed';
  static const String refundedPayment = 'refunded';
  
  // Delivery Status
  static const String pendingDelivery = 'pending';
  static const String pickedUpDelivery = 'picked_up';
  static const String inTransitDelivery = 'in_transit';
  static const String deliveredDelivery = 'delivered';
  
  // KYC Status
  static const String notSubmittedKyc = 'not_submitted';
  static const String pendingKyc = 'pending';
  static const String approvedKyc = 'approved';
  static const String rejectedKyc = 'rejected';
}

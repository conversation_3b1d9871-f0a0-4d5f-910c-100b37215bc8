#!/bin/bash

# Fish Auction App - Quick Start for macOS
# Simplified version that handles common macOS issues

echo "🐟 Fish Auction App - Quick Start (macOS)"
echo "========================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Detect Python command
if command -v python3 >/dev/null 2>&1; then
    PYTHON_CMD="python3"
    PIP_CMD="pip3"
elif command -v python >/dev/null 2>&1; then
    PYTHON_CMD="python"
    PIP_CMD="pip"
else
    echo -e "${RED}❌ Python not found. Please install Python 3.8+${NC}"
    echo "Install from: https://python.org/downloads/"
    exit 1
fi

echo -e "${GREEN}✅ Using Python: $PYTHON_CMD${NC}"

# Check Redis
if ! command -v redis-cli >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️ Redis not found.${NC}"
    if command -v brew >/dev/null 2>&1; then
        echo -e "${BLUE}📦 Installing Redis with Homebrew...${NC}"
        brew install redis
    else
        echo -e "${RED}❌ Redis is required for WebSocket support!${NC}"
        echo ""
        echo "Please install Redis using one of these methods:"
        echo ""
        echo "1. Install Homebrew first, then Redis:"
        echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        echo "   brew install redis"
        echo ""
        echo "2. Or download Redis directly:"
        echo "   https://redis.io/download"
        echo ""
        echo "3. Or use Docker:"
        echo "   docker run -d -p 6379:6379 redis:alpine"
        echo ""
        exit 1
    fi
fi

# Start Redis
if ! redis-cli ping >/dev/null 2>&1; then
    echo -e "${BLUE}🚀 Starting Redis...${NC}"
    brew services start redis
    sleep 2
fi
echo -e "${GREEN}✅ Redis is running${NC}"

# Check ngrok
if ! command -v ngrok >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️ ngrok not found. Installing with Homebrew...${NC}"
    if command -v brew >/dev/null 2>&1; then
        brew install ngrok
    else
        echo -e "${RED}❌ Please install ngrok manually:${NC}"
        echo "brew install ngrok"
        echo "Or download from: https://ngrok.com/download"
        exit 1
    fi
fi
echo -e "${GREEN}✅ ngrok is available${NC}"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo -e "${BLUE}💡 Creating virtual environment...${NC}"
    $PYTHON_CMD -m venv venv
fi

# Activate virtual environment
echo -e "${BLUE}🔧 Activating virtual environment...${NC}"
source venv/bin/activate
echo -e "${GREEN}✅ Virtual environment activated${NC}"

# Install dependencies
echo -e "${BLUE}📦 Installing dependencies...${NC}"
pip install -r requirements.txt

# Install WebSocket dependencies
echo -e "${BLUE}📡 Installing WebSocket dependencies...${NC}"
pip install channels_redis daphne

# Verify Django installation
echo -e "${BLUE}🔍 Verifying Django installation...${NC}"
python -c "import django; print(f'Django {django.get_version()} installed')" || {
    echo -e "${RED}❌ Django installation failed. Trying again...${NC}"
    pip install --upgrade pip
    pip install -r requirements.txt --force-reinstall
    pip install channels_redis daphne
}

# Verify Redis channel layer
echo -e "${BLUE}🔍 Verifying Redis channel layer...${NC}"
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
import django
django.setup()
from channels.layers import get_channel_layer
layer = get_channel_layer()
print(f'Channel layer: {layer.__class__.__name__}')
if 'Redis' not in layer.__class__.__name__:
    raise Exception('Redis channel layer not configured!')
print('✅ Redis channel layer working')
" || {
    echo -e "${RED}❌ Redis channel layer not working${NC}"
    exit 1
}

# Run migrations
echo -e "${BLUE}🗄️ Setting up database...${NC}"
python manage.py migrate

# Kill any existing processes
echo -e "${BLUE}🔄 Cleaning up existing processes...${NC}"
pkill -f "runserver 8000" 2>/dev/null || true
pkill -f "celery.*fish_auction" 2>/dev/null || true
pkill -f "ngrok http 8000" 2>/dev/null || true

# Start Django server with WebSocket support (ASGI required for WebSockets)
echo -e "${BLUE}🚀 Starting Django server with WebSocket support...${NC}"
echo -e "${YELLOW}📡 Using Daphne ASGI server for WebSocket support${NC}"
daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application > django.log 2>&1 &
DJANGO_PID=$!

# Wait and check if Django started
sleep 3
if ! lsof -i :8000 >/dev/null 2>&1; then
    echo -e "${RED}❌ Django server failed to start${NC}"
    echo -e "${YELLOW}📋 Error log:${NC}"
    cat django.log
    exit 1
fi
echo -e "${GREEN}✅ Django server running${NC}"

# Start Celery worker
echo -e "${BLUE}🔄 Starting Celery worker...${NC}"
celery -A fish_auction worker --loglevel=info > celery_worker.log 2>&1 &
CELERY_WORKER_PID=$!

# Start Celery beat
echo -e "${BLUE}⏰ Starting Celery beat...${NC}"
celery -A fish_auction beat --loglevel=info > celery_beat.log 2>&1 &
CELERY_BEAT_PID=$!

# Start ngrok
echo -e "${BLUE}🌐 Starting ngrok tunnel...${NC}"
ngrok http 8000 > ngrok.log 2>&1 &
NGROK_PID=$!

# Wait for services to start
sleep 5

# Test auto-bidding system
echo -e "${BLUE}🧪 Testing auto-bidding system...${NC}"
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
import django
django.setup()
from auctions.models import Auction
live_auctions = Auction.objects.filter(status='live').count()
print(f'✅ Found {live_auctions} live auctions')
print('✅ Auto-bidding system ready')
" || {
    echo -e "${YELLOW}⚠️ No live auctions found - create one in admin panel${NC}"
}

# Get ngrok URL
NGROK_URL=""
for i in {1..10}; do
    NGROK_URL=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null | python -c "
import sys, json
try:
    data = json.load(sys.stdin)
    for tunnel in data.get('tunnels', []):
        if tunnel.get('proto') == 'https':
            print(tunnel.get('public_url', ''))
            break
except:
    pass
" 2>/dev/null)
    
    if [ ! -z "$NGROK_URL" ]; then
        break
    fi
    sleep 1
done

if [ -z "$NGROK_URL" ]; then
    echo -e "${YELLOW}⚠️ Could not get ngrok URL automatically${NC}"
    echo -e "${BLUE}📍 Check ngrok dashboard: http://localhost:4040${NC}"
    NGROK_URL="https://[your-ngrok-url].ngrok.io"
else
    echo -e "${GREEN}✅ ngrok tunnel: $NGROK_URL${NC}"
fi

# Display setup instructions
echo ""
echo -e "${YELLOW}🎯 STRIPE WEBHOOK SETUP${NC}"
echo "================================"
echo -e "${BLUE}📍 Webhook URL: $NGROK_URL/api/payments/stripe/webhook/${NC}"
echo ""
echo "📋 Next steps:"
echo "1. Go to: https://dashboard.stripe.com/webhooks"
echo "2. Click 'Add endpoint'"
echo "3. Enter URL: $NGROK_URL/api/payments/stripe/webhook/"
echo "4. Select events: payment_intent.succeeded, payment_intent.payment_failed"
echo "5. Copy signing secret to .env file"
echo ""
echo -e "${GREEN}🎉 Backend is running!${NC}"
echo "================================"
echo "📱 Django: http://localhost:8000"
echo "🌐 Public: $NGROK_URL"
echo "📊 ngrok: http://localhost:4040"
echo ""
echo "📁 Logs: django.log, celery_worker.log, celery_beat.log"
echo ""
echo -e "${BLUE}🚀 Now start Flutter app:${NC}"
echo "cd fish_auction_app"
echo "flutter pub get"
echo "flutter run -d web-server --web-port 8080"
echo ""
echo -e "${GREEN}🎯 Auto-bidding Features:${NC}"
echo "✅ Real-time bidding with WebSocket"
echo "✅ Asynchronous auto-bidding (one by one)"
echo "✅ Server-side processing (works when app closed)"
echo "✅ Redis channel layer for real-time updates"
echo "✅ Celery workers for background tasks"
echo ""
echo "🛑 Press Ctrl+C to stop all services"

# Cleanup function
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 Stopping services...${NC}"
    
    [ ! -z "$DJANGO_PID" ] && kill $DJANGO_PID 2>/dev/null
    [ ! -z "$CELERY_WORKER_PID" ] && kill $CELERY_WORKER_PID 2>/dev/null
    [ ! -z "$CELERY_BEAT_PID" ] && kill $CELERY_BEAT_PID 2>/dev/null
    [ ! -z "$NGROK_PID" ] && kill $NGROK_PID 2>/dev/null
    
    pkill -f "runserver 8000" 2>/dev/null || true
    pkill -f "celery.*fish_auction" 2>/dev/null || true
    pkill -f "ngrok http 8000" 2>/dev/null || true
    
    echo -e "${GREEN}✅ Cleanup complete${NC}"
    exit 0
}

trap cleanup INT TERM

# Keep running
while true; do
    sleep 1
done

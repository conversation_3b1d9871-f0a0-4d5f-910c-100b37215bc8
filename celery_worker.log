 
 -------------- <EMAIL> v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- macOS-15.4.1-arm64-arm-64bit-Mach-O 2025-06-22 13:04:37
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         fish_auction:0x107ec2a50
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 10 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . auctions.tasks.end_expired_auctions
  . auctions.tasks.monitor_auction_auto_bids
  . auctions.tasks.process_auto_bids
  . auctions.tasks.process_auto_bids_for_auction
  . auctions.tasks.schedule_payment_reminder
  . auctions.tasks.schedule_payment_timeout
  . auctions.tasks.send_auction_ending_reminders
  . auctions.tasks.start_scheduled_auctions
  . fish_auction.celery.debug_task
  . notifications.tasks.cleanup_old_notifications
  . notifications.tasks.end_expired_auctions
  . notifications.tasks.handle_payment_timeouts
  . notifications.tasks.send_auction_ending_reminders
  . notifications.tasks.send_notification_task
  . notifications.tasks.send_payment_deadline_reminders
  . notifications.tasks.send_payment_reminders

[2025-06-22 13:04:38,346: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-06-22 13:04:38,348: INFO/MainProcess] mingle: searching for neighbors
[2025-06-22 13:04:39,355: INFO/MainProcess] mingle: all alone
[2025-06-22 13:04:39,367: INFO/MainProcess] <EMAIL> ready.
[2025-06-22 13:04:39,370: INFO/MainProcess] Task auctions.tasks.end_expired_auctions[573d77d9-7b2c-4a20-b720-ba2153f31e45] received
[2025-06-22 13:04:39,393: INFO/ForkPoolWorker-8] Ended 0 expired auctions
[2025-06-22 13:04:39,399: INFO/ForkPoolWorker-8] Task auctions.tasks.end_expired_auctions[573d77d9-7b2c-4a20-b720-ba2153f31e45] succeeded in 0.02806300000520423s: 'Ended 0 auctions'

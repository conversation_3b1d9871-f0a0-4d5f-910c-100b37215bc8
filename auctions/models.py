from django.db import models
from django.conf import settings
from decimal import Decimal
from django.utils import timezone
from datetime import timedelta


class FishCategory(models.Model):
    """Fish categories for auctions"""

    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    image = models.ImageField(upload_to='fish_categories/', blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Fish Categories"


class Auction(models.Model):
    """Main auction model"""

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('scheduled', 'Scheduled'),
        ('live', 'Live'),
        ('ended', 'Ended'),
        ('cancelled', 'Cancelled'),
        ('failed', 'Failed'),
        ('payment_pending', 'Payment Pending'),
        ('completed', 'Completed'),
    ]

    AUCTION_TYPE_CHOICES = [
        ('live', 'Live Auction'),
        ('timed', 'Timed Auction'),
        ('buy_now', 'Buy Now'),
    ]

    seller = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='auctions_created')
    title = models.CharField(max_length=200)
    description = models.TextField()
    fish_category = models.ForeignKey(FishCategory, on_delete=models.CASCADE, related_name='auctions')

    # Auction details
    auction_type = models.CharField(max_length=20, choices=AUCTION_TYPE_CHOICES, default='live')
    starting_price = models.DecimalField(max_digits=10, decimal_places=2)
    reserve_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    target_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, help_text="Seller's target price for the auction")
    buy_now_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    current_price = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    bid_increment = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'))

    # Fish details
    fish_type = models.CharField(max_length=100)
    weight = models.DecimalField(max_digits=8, decimal_places=2, help_text="Weight in kg")
    quantity = models.PositiveIntegerField(default=1)
    catch_date = models.DateField()
    catch_location = models.CharField(max_length=200)

    # Timing
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()

    # Status and winner
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    winner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='auctions_won'
    )

    # Payment window
    payment_deadline = models.DateTimeField(blank=True, null=True)
    payment_received = models.BooleanField(default=False)

    # Media
    main_image = models.ImageField(upload_to='auction_images/')

    # Metadata
    total_bids = models.PositiveIntegerField(default=0)
    total_bidders = models.PositiveIntegerField(default=0)
    views_count = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} - {self.fish_type}"

    def save(self, *args, **kwargs):
        # Set payment deadline when auction ends
        if self.status == 'ended' and not self.payment_deadline:
            self.payment_deadline = timezone.now() + timedelta(minutes=20)
        super().save(*args, **kwargs)

    @property
    def is_live(self):
        now = timezone.now()
        return self.status == 'live' and self.start_time <= now <= self.end_time

    @property
    def time_remaining(self):
        if self.status == 'live':
            return max(timedelta(0), self.end_time - timezone.now())
        return timedelta(0)

    @property
    def target_price_reached(self):
        """Check if target price has been reached"""
        if not self.target_price:
            return False
        return self.current_price >= self.target_price

    def can_close_early(self):
        """Check if seller can close auction early"""
        return (
            self.status == 'live' and
            self.target_price and
            self.current_price >= self.target_price
        )

    class Meta:
        ordering = ['-created_at']


class AuctionImage(models.Model):
    """Additional images for auctions"""

    auction = models.ForeignKey(Auction, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='auction_images/')
    caption = models.CharField(max_length=200, blank=True)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']


class Bid(models.Model):
    """Bid model for auctions"""

    BID_TYPE_CHOICES = [
        ('manual', 'Manual'),
        ('auto', 'Automatic'),
    ]

    auction = models.ForeignKey(Auction, on_delete=models.CASCADE, related_name='bids')
    bidder = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='bids')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    bid_type = models.CharField(max_length=10, choices=BID_TYPE_CHOICES, default='manual')
    timestamp = models.DateTimeField(auto_now_add=True)

    # For automatic bidding
    is_winning = models.BooleanField(default=False)

    def __str__(self):
        return f"Bid of ${self.amount} by {self.bidder.username} on {self.auction.title}"

    class Meta:
        ordering = ['-timestamp']
        unique_together = ['auction', 'bidder', 'timestamp']


class AutoBid(models.Model):
    """Automatic bidding configuration"""

    auction = models.ForeignKey(Auction, on_delete=models.CASCADE, related_name='auto_bids')
    bidder = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='auto_bids')
    max_amount = models.DecimalField(max_digits=10, decimal_places=2)
    increment = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'))
    is_active = models.BooleanField(default=True)
    current_bid_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"AutoBid by {self.bidder.username} on {self.auction.title} (Max: ${self.max_amount})"

    class Meta:
        unique_together = ['auction', 'bidder']


class AuctionWatchlist(models.Model):
    """User's auction watchlist"""

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='watchlist')
    auction = models.ForeignKey(Auction, on_delete=models.CASCADE, related_name='watchers')
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'auction']

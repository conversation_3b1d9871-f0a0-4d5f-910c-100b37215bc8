import os
from celery import Celery

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')

app = Celery('fish_auction')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery Beat Schedule for periodic tasks
app.conf.beat_schedule = {
    'start-scheduled-auctions': {
        'task': 'auctions.tasks.start_scheduled_auctions',
        'schedule': 60.0,  # Run every minute
    },
    'end-expired-auctions': {
        'task': 'auctions.tasks.end_expired_auctions',
        'schedule': 60.0,  # Run every minute
    },
    'monitor-auction-auto-bids': {
        'task': 'auctions.tasks.monitor_auction_auto_bids',
        'schedule': 5.0,  # Run every 5 seconds for real-time auto-bidding
    },
    'send-auction-ending-reminders': {
        'task': 'notifications.tasks.send_auction_ending_reminders',
        'schedule': 300.0,  # Run every 5 minutes
    },
    'send-payment-reminders': {
        'task': 'notifications.tasks.send_payment_reminders',
        'schedule': 1800.0,  # Run every 30 minutes
    },
    'handle-payment-timeouts': {
        'task': 'notifications.tasks.handle_payment_timeouts',
        'schedule': 60.0,  # Run every minute
    },
    'send-payment-deadline-reminders': {
        'task': 'notifications.tasks.send_payment_deadline_reminders',
        'schedule': 60.0,  # Run every minute
    },
    'cleanup-old-notifications': {
        'task': 'notifications.tasks.cleanup_old_notifications',
        'schedule': 86400.0,  # Run daily (24 hours)
    },
}

app.conf.timezone = 'UTC'


@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
